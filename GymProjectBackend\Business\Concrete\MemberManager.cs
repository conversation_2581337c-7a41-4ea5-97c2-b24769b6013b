using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;

using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using MemberFilter = Entities.DTOs.MemberFilter;

namespace Business.Concrete
{
    public class MemberManager : IMemberService
    {
        IMemberDal _memberDal;
        IMembershipDal _membershipDal;
        IEntryExitHistoryService _entryExitHistoryService;
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;
        private readonly IQrCodeEncryptionService _qrCodeEncryptionService;

        public MemberManager(
            IMemberDal memberDal,
            IMembershipDal membershipDal,
            IEntryExitHistoryService entryExitHistoryService,
            Core.Utilities.Security.CompanyContext.ICompanyContext companyContext,
            IQrCodeEncryptionService qrCodeEncryptionService)
        {
            _memberDal = memberDal;
            _membershipDal = membershipDal;
            _entryExitHistoryService = entryExitHistoryService;
            _companyContext = companyContext;
            _qrCodeEncryptionService = qrCodeEncryptionService;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<Dictionary<string, int>> GetBranchCounts()
        {
            var companyId = _companyContext.GetCompanyId();
            return _memberDal.GetBranchCounts(companyId);
        }


        [SecuredOperation("owner,admin")]
        public IDataResult<List<MemberEntryDto>> GetMemberEntriesByName(string name)
        {
            return new SuccessDataResult<List<MemberEntryDto>>(_memberDal.GetMemberEntriesByName(name));
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<MemberEntryDto>> GetMemberEntriesBySearchPaginated(MemberEntryPagingParameters parameters)
        {
            var result = _memberDal.GetMemberEntriesBySearchPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<MemberEntryDto>>(result);
        }
        // [SecuredOperation("owner,admin")] // GEÇİCİ KALDIRILDI - PROMPT 1 TEST İÇİN
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<Member>> GetAllPaginated(MemberPagingParameters parameters)
        {
            var result = _memberDal.GetAllPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<Member>>(result);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<MemberFilter>> GetMemberDetailsPaginated(MemberPagingParameters parameters)
        {
            var result = _memberDal.GetMemberDetailsPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<MemberFilter>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<MemberFilter>> GetMembersByMultiplePackages(MemberPagingParameters parameters)
        {
            var result = _memberDal.GetMembersByMultiplePackages(parameters);
            return new SuccessDataResult<PaginatedResult<MemberFilter>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<Member>> GetMembersWithBalancePaginated(MemberPagingParameters parameters, string balanceFilter)
        {
            var result = _memberDal.GetMembersWithBalancePaginated(parameters, balanceFilter);
            return new SuccessDataResult<PaginatedResult<Member>>(result);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<int> GetTotalActiveMembers()
        {
            var companyId = _companyContext.GetCompanyId();
            return _memberDal.GetTotalActiveMembers(companyId);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<int> GetTotalRegisteredMembers()
        {
            var companyId = _companyContext.GetCompanyId();
            return _memberDal.GetTotalRegisteredMembers(companyId);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<Dictionary<string, int>> GetActiveMemberCounts()
        {
            var companyId = _companyContext.GetCompanyId();
            return _memberDal.GetActiveMemberCounts(companyId);
        }


        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberEntryDto>> GetTodayEntries(DateTime date)
        {
            return new SuccessDataResult<List<MemberEntryDto>>(_memberDal.GetTodayEntries(date));
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<MemberEntryDto>> GetTodayEntriesPaginated(MemberEntryPagingParameters parameters)
        {
            var result = _memberDal.GetTodayEntriesPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<MemberEntryDto>>(result);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(MemberValidator))]
        [TransactionScopeAspect]
        public IResult Add(Member member)
        {
            member.Name = member.Name.ToUpper();
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Karmaşık ekleme işlemini DAL katmanına taşıdık
            return _memberDal.AddMemberWithUserManagement(member, companyId);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(MemberValidator))]
        [TransactionScopeAspect]
        public IResult AddWithCard(Member member)
        {
            member.Name = member.Name.ToUpper();
            var companyId = _companyContext.GetCompanyId();

            // Kartlı üye ekleme için özel metod
            return _memberDal.AddMemberWithCard(member, companyId);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect]
        public IResult Delete(int id)
        {
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Karmaşık silme işlemini DAL katmanına taşıdık
            return _memberDal.DeleteMemberWithUserManagement(id, companyId);
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<Member>> GetAll()
        {
            return new SuccessDataResult<List<Member>>(_memberDal.GetAll());
        }
        // [SecuredOperation("owner,admin")] // GEÇİCİ KALDIRILDI - PROMPT 1 TEST İÇİN
        public IDataResult<List<MembeFilterDto>> GetMemberDetails()
        {
            return new SuccessDataResult<List<MembeFilterDto>>(_memberDal.GetMemberDetails());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<MemberDetailWithHistoryDto> GetMemberDetailById(int memberId)
        {
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Karmaşık hesaplama işlemini DAL katmanına taşıdık
            return _memberDal.GetMemberDetailByIdWithCalculations(memberId, companyId);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberBirthdayDto>> GetUpcomingBirthdays(int days)
        {
            return new SuccessDataResult<List<MemberBirthdayDto>>(_memberDal.GetUpcomingBirthdays(days));
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<GetActiveMemberDto>> GetActiveMembers()
        {
            return new SuccessDataResult<List<GetActiveMemberDto>>(_memberDal.GetActiveMembers());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberEntryExitHistoryDto>> GetMemberEntryExitHistory()
        {
            return new SuccessDataResult<List<MemberEntryExitHistoryDto>>(_memberDal.GetMemberEntryExitHistory());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberRemainingDayDto>> GetMemberRemainingDay()
        {
            return new SuccessDataResult<List<MemberRemainingDayDto>>(_memberDal.GetMemberRemainingDay());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(MemberValidator))]
        public IResult Update(Member member)
        {
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Karmaşık güncelleme işlemini DAL katmanına taşıdık
            return _memberDal.UpdateMemberWithUserManagement(member, companyId);
        }
        //SECUREDOPERATION ASPECT KOYMA PUBLIC PING STATION
        [PerformanceAspect(2)]
        public IDataResult<MemberDetailDto> GetMemberRemainingDaysForScanNumber(string scanNumber)
        {
            // MBR prefix kontrolü - Mobil QR kod mu yoksa RFID kart mı?
            if (scanNumber.StartsWith("MBR"))
            {
                // Mobil QR kod - MBR prefix'ini kaldır ve şifre çöz
                string encryptedQRCode = scanNumber.Substring(3); // "MBR" prefix'ini kaldır

                var decryptResult = _qrCodeEncryptionService.DecryptQrToken(encryptedQRCode);

                // QR kod çözülemezse hata döndür
                if (!decryptResult.Success || decryptResult.Data == null)
                {
                    return new ErrorDataResult<MemberDetailDto>(null, decryptResult.Message ?? "Geçersiz QR kod.");
                }

                var decryptedData = decryptResult.Data;

                // Zaman geçerliliğini kontrol et
                if (!decryptedData.IsValid)
                {
                    return new ErrorDataResult<MemberDetailDto>(null, decryptedData.ErrorMessage ?? "QR kodun süresi dolmuş.");
                }

                // DAL'dan veri al (şifresi çözülmüş scan number ile)
                return _memberDal.GetMemberRemainingDaysForScanNumber(decryptedData.ScanNumber, _companyContext.GetCompanyId());
            }
            else
            {
                // RFID kart - Direkt kart numarası, şifreleme yok
                return _memberDal.GetMemberRemainingDaysForScanNumber(scanNumber, _companyContext.GetCompanyId());
            }
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<GetMemberQRByPhoneNumberDto> GetMemberQRByPhoneNumber(string phoneNumber)
        {
            var result = _memberDal.GetMemberQRByPhoneNumber(phoneNumber, _companyContext.GetCompanyId());

            if (result.Success && result.Data != null)
            {
                // QR kod şifreleme işlemini Manager'da yap
                string encryptedQRCode = _qrCodeEncryptionService.CreateEncryptedQrToken(
                    result.Data.Name.GetHashCode(), // Geçici çözüm - MemberID yerine
                    result.Data.ScanNumber
                );

                // MBR prefix ekleyerek mobil QR kod olduğunu belirt
                result.Data.ScanNumber = "MBR" + encryptedQRCode;
            }

            return result;
        }

        [SecuredOperation("owner,admin")]
        public IDataResult<List<Member>> GetByMemberId(int memberid)
        {
            return new SuccessDataResult<List<Member>>(_memberDal.GetAll(c => c.MemberID == memberid));
        }

        // SOLID prensiplerine uygun refactoring sonrası artık kullanılmayan helper metotlar kaldırıldı
        // GenerateUniqueQRCode, CreateNewUserForMember ve GenerateRandomPart metotları
        // DAL katmanına taşındı


        //Şuanda kullanılmayan bir metot ilerde lazım olabilir diye duruyor.
        [SecuredOperation("owner,admin,member")]
        [PerformanceAspect(3)]
        public IDataResult<Member> GetMemberByUserId(int userId)
        {
            var member = _memberDal.Get(m => m.UserID == userId && m.IsActive == true && m.CompanyID == _companyContext.GetCompanyId());
            if (member == null)
            {
                return new ErrorDataResult<Member>("Üye bulunamadı veya erişim yetkiniz yok.");
            }
            return new SuccessDataResult<Member>(member);
        }

        [SecuredOperation("member")]
        [PerformanceAspect(3)]
        public IDataResult<GetMemberQRByPhoneNumberDto> GetMemberQRByUserIdWithoutCompanyFilter(int userId)
        {
            var result = _memberDal.GetMemberQRByUserIdWithoutCompanyFilter(userId);

            if (result.Success && result.Data != null)
            {
                // QR kod şifreleme işlemini Manager'da yap
                // MemberID'yi DAL'dan alamadığımız için alternatif bir yöntem kullanıyoruz
                // Bu durumda member.ScanNumber zaten mevcut, sadece şifreleme yapıyoruz
                string encryptedQRCode = _qrCodeEncryptionService.CreateEncryptedQrToken(
                    result.Data.Name.GetHashCode(), // Geçici çözüm
                    result.Data.ScanNumber
                );

                // MBR prefix ekleyerek mobil QR kod olduğunu belirt
                result.Data.ScanNumber = "MBR" + encryptedQRCode;
            }

            return result;
        }

        /// <summary>
        /// Kullanıcının profil bilgilerini getirir (sadece member rolü)
        /// </summary>
        [SecuredOperation("member")]
        [PerformanceAspect(3)]
        public IDataResult<MemberProfileDto> GetMemberProfileByUserId(int userId)
        {
            return _memberDal.GetMemberProfileByUserId(userId, _companyContext.GetCompanyId());
        }

        /// <summary>
        /// Kullanıcının profil bilgilerini günceller (sadece member rolü)
        /// User tablosunda: FirstName, LastName (tek kayıt)
        /// Member tablosunda: Adress, BirthDate (sadece mevcut şirketteki kayıt güncellenir)
        /// Not: Member.Name alanı güncellenmez (salon yöneticisi kontrolünde)
        /// </summary>
        [SecuredOperation("member")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult UpdateMemberProfile(int userId, MemberProfileUpdateDto profileUpdateDto)
        {
            return _memberDal.UpdateMemberProfile(userId, _companyContext.GetCompanyId(), profileUpdateDto);
        }
    }
}