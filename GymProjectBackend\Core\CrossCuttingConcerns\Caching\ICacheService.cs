using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Cache service interface for Redis operations
    /// Multi-tenant aware cache service with generic type support
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// Get cached value by key with generic type support
        /// </summary>
        /// <typeparam name="T">Type of cached object</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached object or default(T) if not found</returns>
        T Get<T>(string key);

        /// <summary>
        /// Get cached value by key with generic type support (async)
        /// </summary>
        /// <typeparam name="T">Type of cached object</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cached object or default(T) if not found</returns>
        Task<T> GetAsync<T>(string key);

        /// <summary>
        /// Set cache value with expiry time
        /// </summary>
        /// <typeparam name="T">Type of object to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Object to cache</param>
        /// <param name="expiry">Expiry time (optional, default: 30 minutes)</param>
        void Set<T>(string key, T value, TimeSpan? expiry = null);

        /// <summary>
        /// Set cache value with expiry time (async)
        /// </summary>
        /// <typeparam name="T">Type of object to cache</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Object to cache</param>
        /// <param name="expiry">Expiry time (optional, default: 30 minutes)</param>
        Task SetAsync<T>(string key, T value, TimeSpan? expiry = null);

        /// <summary>
        /// Remove cached value by key
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>True if removed, false if not found</returns>
        bool Remove(string key);

        /// <summary>
        /// Remove cached value by key (async)
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>True if removed, false if not found</returns>
        Task<bool> RemoveAsync(string key);

        /// <summary>
        /// Check if key exists in cache
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>True if exists, false otherwise</returns>
        bool Exists(string key);

        /// <summary>
        /// Check if key exists in cache (async)
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>True if exists, false otherwise</returns>
        Task<bool> ExistsAsync(string key);

        /// <summary>
        /// Get all keys matching pattern
        /// </summary>
        /// <param name="pattern">Pattern to match (default: "*")</param>
        /// <returns>List of matching keys</returns>
        IEnumerable<string> GetKeys(string pattern = "*");

        /// <summary>
        /// Get all keys matching pattern (async)
        /// </summary>
        /// <param name="pattern">Pattern to match (default: "*")</param>
        /// <returns>List of matching keys</returns>
        Task<IEnumerable<string>> GetKeysAsync(string pattern = "*");

        /// <summary>
        /// Remove all keys matching pattern
        /// </summary>
        /// <param name="pattern">Pattern to match</param>
        /// <returns>Number of keys removed</returns>
        long RemoveByPattern(string pattern);

        /// <summary>
        /// Remove all keys matching pattern (async)
        /// </summary>
        /// <param name="pattern">Pattern to match</param>
        /// <returns>Number of keys removed</returns>
        Task<long> RemoveByPatternAsync(string pattern);

        /// <summary>
        /// Get cache statistics and health info
        /// </summary>
        /// <returns>Cache health information</returns>
        CacheHealthInfo GetHealthInfo();

        /// <summary>
        /// Test Redis connection
        /// </summary>
        /// <returns>True if connection is healthy</returns>
        bool IsHealthy();
    }

    /// <summary>
    /// Cache health information
    /// </summary>
    public class CacheHealthInfo
    {
        public bool IsConnected { get; set; }
        public string ServerInfo { get; set; }
        public long DatabaseSize { get; set; }
        public TimeSpan PingTime { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime CheckTime { get; set; } = DateTime.UtcNow;
    }
}
