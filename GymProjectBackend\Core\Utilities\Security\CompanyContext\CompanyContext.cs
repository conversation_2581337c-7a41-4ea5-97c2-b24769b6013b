using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace Core.Utilities.Security.CompanyContext
{
    public class CompanyContext : ICompanyContext
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CompanyContext(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public int GetCompanyId()
        {
            var companyIdClaim = _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type == "CompanyId");

            if (companyIdClaim != null && int.TryParse(companyIdClaim.Value, out int companyId))
            {
                return companyId;
            }

            // Eğer claim bulunamazsa veya geçerli bir de<PERSON><PERSON>, varsay<PERSON>lan bir değer döndür
            // Bu durumda -1 döndürüyoruz, bu da geçersiz bir CompanyID olduğunu gösterir
            return -1;
        }
    }
}
